# frozen_string_literal: true

# Mission Control Jobs configuration with super admin authentication
# This configuration must be set before the engine is loaded
Rails
  .application
  .config
  .before_configuration do
    # Disable HTTP Basic Authentication completely
    Rails.application.config.mission_control.jobs.http_basic_auth_enabled =
      false

    # Set the base controller class to integrate with our super admin authentication system
    Rails.application.config.mission_control.jobs.base_controller_class =
      'SuperAdmin::MissionControlJobsController'
  end
