# frozen_string_literal: true

# Mission Control Jobs configuration with super admin authentication
Rails.application.configure do
  # Disable default HTTP Basic Authentication since we're using custom authentication
  config.mission_control.jobs.http_basic_auth_enabled = false
  
  # Set the base controller class to integrate with our super admin authentication system
  config.mission_control.jobs.base_controller_class = "SuperAdmin::MissionControlJobsController"
end

# Create a custom controller that inherits from SuperAdmin::BaseController
# This ensures all Mission Control Jobs routes use our existing super admin authentication
class SuperAdmin::MissionControlJobsController < SuperAdmin::BaseController
  # Skip the resource permissions check since Mission Control Jobs handles its own authorization
  skip_before_action :check_resource_permissions, if: :mission_control_jobs_request?
  
  # Override the layout to use Mission Control Jobs' own layout
  layout false
  
  private
  
  # Check if this is a Mission Control Jobs request
  def mission_control_jobs_request?
    request.path.start_with?('/jobs')
  end
  
  # Override require_admin_access to provide more specific error handling for Mission Control Jobs
  def require_admin_access
    # During impersonation, check the original admin user's permissions
    user_to_check = if Current.impersonating?
                      User.find_by(id: Current.impersonator_id)
                    else
                      Current.user
                    end

    unless user_to_check&.can_access_admin?
      # For Mission Control Jobs, we want to render a proper error page instead of plain text
      if request.format.html?
        render_mission_control_access_denied
      else
        render plain: 'Access denied. Administrative privileges required.',
               status: :forbidden
      end
      return
    end
  end
  
  # Render a proper access denied page for Mission Control Jobs
  def render_mission_control_access_denied
    render html: <<~HTML.html_safe, status: :forbidden, layout: false
      <!DOCTYPE html>
      <html>
        <head>
          <title>Access Denied - Mission Control Jobs</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
              background-color: #f8fafc;
              color: #374151;
              margin: 0;
              padding: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              min-height: 100vh;
            }
            .container {
              text-align: center;
              max-width: 500px;
              padding: 2rem;
              background: white;
              border-radius: 8px;
              box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            .icon {
              font-size: 4rem;
              margin-bottom: 1rem;
            }
            h1 {
              color: #dc2626;
              margin-bottom: 1rem;
              font-size: 1.5rem;
            }
            p {
              margin-bottom: 1.5rem;
              line-height: 1.6;
            }
            .btn {
              display: inline-block;
              background-color: #6b7280;
              color: white;
              padding: 0.75rem 1.5rem;
              text-decoration: none;
              border-radius: 6px;
              font-weight: 500;
              transition: background-color 0.2s;
            }
            .btn:hover {
              background-color: #4b5563;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="icon">🔒</div>
            <h1>Access Denied</h1>
            <p>
              You need super admin privileges to access Mission Control Jobs dashboard.
              This area is restricted to authorized administrators only.
            </p>
            <a href="#{super_admin_admin_dashboard_path}" class="btn">Return to Admin Dashboard</a>
          </div>
        </body>
      </html>
    HTML
  rescue NameError
    # Fallback if super_admin_admin_dashboard_path is not available
    render html: <<~HTML.html_safe, status: :forbidden, layout: false
      <!DOCTYPE html>
      <html>
        <head>
          <title>Access Denied</title>
          <style>
            body { font-family: sans-serif; text-align: center; padding: 2rem; }
            h1 { color: #dc2626; }
          </style>
        </head>
        <body>
          <h1>Access Denied</h1>
          <p>Super admin privileges required to access Mission Control Jobs.</p>
        </body>
      </html>
    HTML
  end
end
